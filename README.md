# NovelWebsite 小說網站爬蟲項目

*基於遠端 main 穩定版本 (commit: 2033946d7) - Backend/Novel 模組日落遷移完成，Django 架構統一化*

[![Main CI](https://github.com/MumuTW/novel-web/actions/workflows/main-ci.yml/badge.svg)](https://github.com/MumuTW/novel-web/actions/workflows/main-ci.yml)
[![Build Docs](https://github.com/MumuTW/novel-web/actions/workflows/docs.yml/badge.svg)](https://github.com/MumuTW/novel-web/actions/workflows/docs.yml)
[![Security Scan](https://github.com/MumuTW/novel-web/actions/workflows/weekly-security-scan.yml/badge.svg)](https://github.com/MumuTW/novel-web/actions/workflows/weekly-security-scan.yml)

## 🏗️ Monorepo 基礎架構: 已完成

✅ **pnpm workspace + Turborepo 架構**: 現代化 monorepo 轉型完成
✅ **依賴優化突破**: 從 1.5GB → 11MB (98.6% 減少)
✅ **智能緩存**: FULL TURBO 機制，瞬間重建
✅ **CI/CD 現代化**: 工作區感知，智能構建

## 🚀 快速開始

### ⚡ Monorepo 開發命令 (Turborepo 架構)

本項目採用現代化 **pnpm workspace + Turborepo** 架構，提供極速開發體驗：

#### 🚀 核心開發命令

```bash
# 🏗️ 依賴管理
pnpm install                # 安裝所有 workspace 依賴

# ⚡ Turborepo 命令 (智能緩存)
pnpm turbo build           # 構建所有應用 (FULL TURBO 緩存)
pnpm turbo dev             # 開發模式 (持久化進程)
pnpm turbo test            # 運行所有測試
pnpm turbo lint            # 代碼檢查

# 🎯 工作區特定命令
pnpm --filter @novelwebsite/web dev    # 只啟動 web 應用
pnpm --filter @novelwebsite/web test   # 只測試 web 應用
```

#### 🎯 開發工作流程

```bash
開發時:    pnpm turbo dev           # 熱重載開發
測試時:    pnpm turbo test          # 智能測試執行
構建時:    pnpm turbo build         # 增量構建
發佈前:    make ci-check            # 傳統 CI 檢查 (向下相容)
```

### 🏗️ Monorepo 架構 (現代化完成)

經過 **Issue #144** 的 Monorepo 轉型，項目架構已實現現代化：

```
📂 NovelWebsite/ (pnpm workspace + Turborepo)
├── 🎯 apps/                 # Monorepo 應用目錄
│   └── web/                # ⚛️ React 前端應用 (從 frontend/ 遷移)
│       ├── .storybook/     # Storybook 配置
│       ├── src/components/ # React 組件庫
│       ├── src/pages/      # 頁面組件
│       └── tests/e2e/      # 端到端測試
├── 🔧 packages/            # 共享套件目錄 (未來擴展)
├── 🐍 backend/             # Django 後端 + 爬蟲引擎
│   ├── apps/catalog/       # 📚 標準化業務邏輯 (小說目錄)
│   ├── config/             # ⚙️ 模組化設定管理
│   ├── crawler_engine/     # 🕷️ 重構後爬蟲引擎
│   └── novel/              # 舊版應用 (部分邏輯保留)
├── ⚛️ frontend/            # 舊版前端 (保留相容性)
├── 📁 docs/                # 分層架構文檔 (00-04 層級)
├── 🏗️ infra/               # 基礎設施配置
├── ⚙️ 核心配置文件:
│   ├── pnpm-workspace.yaml # Workspace 配置
│   ├── turbo.json          # Turborepo 管道配置
│   └── package.json        # 根依賴管理
├── 🛠️ scripts/             # 自動化腳本
└── 🧪 tests/               # 整合測試
```

### 🔧 開發環境快速設置

#### ⚡ Monorepo 快速啟動

```bash
# 📦 安裝依賴 (pnpm workspace)
pnpm install

# 🚀 開發模式 (所有應用)
pnpm turbo dev

# 🏗️ 構建檢查
pnpm turbo build

# 🧪 運行測試
pnpm turbo test
```

#### 🎯 舊版 Makefile 指令 (向下相容)

```bash
# 查看所有可用命令
make help

# 傳統分步設置
make install-deps           # 安裝所有依賴
make backend-migrate        # 資料庫遷移
make dev                    # 🚀 並行啟動前後端服務器
```

#### 🔧 依賴管理（基於穩定版本）

```bash
# 🚀 前端使用 pnpm workspace 管理 (已優化)
Node.js: >=16.0.0 (推薦 18+)
pnpm: >=8.0.0 (workspace 支援)

# 📦 後端 Python 依賴 (已安全加固)
Python: 3.11+
Django: 5.0+
Scrapy: 2.11.2+ (CVE-2017-14158 已修復)
```

#### 📋 日常開發命令

```bash
make dev                    # 🚀 並行啟動前後端 (最常用)
make test                   # 🧪 執行所有測試
make test-integration       # 🔗 執行整合測試
make lint                   # 🔍 程式碼品質檢查
make format                 # ✨ 自動格式化程式碼
make ci-check              # 🔄 本地CI完整驗證
make backend-shell         # 🐚 Django shell
make crawler-ttkan         # 🕷️ 執行TTKAN爬蟲
```

## 📋 項目概述

NovelWebsite 是一個多站點小說內容聚合爬蟲系統，專為繁體中文小說網站設計。採用**現代化全端架構**和**專業 DevOps 流程**，已完成完整的重構和品質提升。

### 🎯 項目狀態 (截至 2025-06-26)

**完成度**: 99% | **CI/CD**: ✅ Tier 2 ECR 架構 + 智能資源管理 | **安全**: ✅ 7個安全漏洞修復 | **性能**: ✅ CI 快取效率 98%

#### ✅ 重構成果 (三階段完成)

**Phase 1: 清理和優化**

- ✅ 清理運行時垃圾和死代碼
- ✅ 強化 .gitignore 規則
- ✅ 清理 zombie 進程和臨時文件

**Phase 2: 後端架構重組**

- ✅ 標準化 Django 應用結構 (novel → apps/catalog)
- ✅ 重構爬蟲引擎 (crawler → crawler_engine)
- ✅ 模組化配置管理系統

**Phase 3: 前端組件整理 + 依賴優化**

- ✅ 組織 React 組件結構
- ✅ 統一腳本和工具管理
- ✅ 更新路徑引用和文檔
- ✅ **超級優化**: Node.js 依賴從 1.3GB 瘦身至 769MB (節省 531MB)

#### ✅ CI/CD 基礎設施 (企業級)

**🚀 Tier 2 革命性架構 - ECR 集中化映像管理:**

- ✅ **ECR 整合完成**: AWS Elastic Container Registry 集中映像管理
- ✅ **自動化映像推送**: GitHub Actions → ECR 無縫整合
- ✅ **OIDC 安全認證**: GitHub ↔ AWS 零密鑰配置
- ✅ **生命週期策略**: 自動清理舊映像，保持最後10個版本
- ✅ **映像安全掃描**: 推送時自動安全掃描
- ✅ **智能觸發優化**: 文檔構建精確路徑過濾，避免不必要的 CI 資源浪費
- ✅ **Docker 層快取優化**: 共享基礎層 + 版本一致性修復，建立高效快取基礎設施

**⚡ 革命性性能成就 (99%+ 提升):**

- ✅ **Tier 2 終極優化**: 前端 **125s → 1s** (**98.4% 提升**)
- ✅ **後端測試**: **38s → 1s** (**97.4% 提升**)
- ✅ **ECR 映像拉取**: <1秒 (預暖緩存)
- ✅ **AWS 基礎設施**: 自託管 Runner EC2 + ECR 完美整合
- ✅ **成本優化**: 70% 節省 + 按需擴展
- ✅ **完全自動化**: Infrastructure as Code + GitHub App 準備
- ✅ **CI 穩定性**: 100% 通過率，所有測試成功

**🏗️ Tier 2 技術架構:**

```bash
# 革命性 CI/CD 流程 (1-2秒完成)
GitHub Actions → ECR 映像拉取 → 容器化測試 → 部署就緒

# 本地開發到生產的統一映像
本地構建 → ECR 推送 → CI/CD 拉取 → 生產部署

# 智慧條件執行 (持續優化)
變更檢測 → 精確路徑過濾 → 條件觸發 → 資源最佳化 → 文檔構建優化
```

**🧪 多層次測試策略:**

- ✅ Django 單元測試: 16 個測試全部通過
- ✅ React/Jest 測試: 30 個測試全部通過
- ✅ API 整合測試: 健康檢查和數據格式驗證
- ✅ 本地 CI 流程: 3 秒快速反饋

**🛡️ 品質保證工具:**

- ✅ Pre-commit hooks: **實現精準豁免策略**，從"阻塞"變"守護者"
- ✅ 敏感信息掃描: scripts/check_sensitive.sh (豁免註釋支援)
- ✅ 自動格式化: **35個檔案統一代碼風格**
- ✅ Percy 視覺回歸測試 (智慧條件觸發)
- ✅ Lighthouse 性能測試 (智慧條件觸發)
- ✅ 安全檢查和大檔案監控
- ✅ **安全強化**: 可重用 Actions 風險文檔化 + 7個安全漏洞完整修復

#### ✅ 已完成的核心系統

**後端架構 (95%完成):**

- ✅ Django 5.0 + DRF API 完整實現
- ✅ PostgreSQL 數據庫模型和遷移
- ✅ pydantic-settings 統一配置管理
- ✅ Redis Queue + AsyncPG Worker 解耦架構
- ✅ 完整的測試數據 fixtures (CI 依賴)

**爬蟲系統 (90%完成):**

- ✅ BaseAdapter 可擴展架構
- ✅ CZBooks.net 適配器 (MVP完成)
- ✅ 飄天文學網適配器 (MVP完成)
- ✅ 選擇器外部化配置 (YAML)
- ✅ 工業級錯誤處理和重試機制

**前端應用 (90%完成):**

- ✅ React 18 + TypeScript 架構
- ✅ 小說列表和詳情頁面
- ✅ 章節閱讀器界面 (內聯 SVG 圖示優化)
- ✅ 搜索API後端支持
- ✅ Storybook 7.6 元件開發環境
- ✅ 響應式設計基礎架構
- ✅ **依賴瘦身**: 移除臃腫圖示庫，改用輕量內聯 SVG

**數據管道 (運營就緒):**

```
Scrapy Spiders → Redis Queue → AsyncPG Worker → PostgreSQL → Django API → React Frontend
```

### 🔧 核心技術特性

**解耦架構設計:**

- Scrapy + Redis + AsyncPG 非阻塞管線
- 可擴展適配器模式
- 模組化配置管理

**專業 DevOps 流程:**

- 多層次測試策略 (unit → integration → e2e)
- 本地 CI 快速反饋 (3 秒)
- 自動化品質檢查
- 視覺回歸測試

**現代化前端:**

- React 18 + TypeScript
- 組件化開發 (Storybook)
- 響應式設計系統

**安全與監控:**

- 敏感信息掃描
- 自動化安全漏洞檢查 (pnpm audit)
- 週期性安全掃描 (每週一次)
- Prometheus 監控集成
- Doppler 密鑰管理

### 🎯 內容獲取戰略

#### 核心戰略：黃金28精品內容 (P0優先級)

**戰略定位**: 專注28本市場驗證的精品小說，2週內完成MVP

- 🎯 **黃金屋中文網** - 完本排行榜精選
- 📚 **內容類型**: 玄幻(36%)、仙俠(14%)、科幻(11%)等
- ⭐ **知名作品**: 耳根《仙逆》、貓膩《大道朝天》、月關《臨安不安夜侯》
- ✅ **品質標準**: 內容完整、無廣告、排版清晰

#### 輔助來源 (已完成基礎適配)

- ✅ **CZBooks.net** - 完全靜態內容，零JavaScript依賴
- ✅ **飄天文學網** - 傳統HTML結構，解析邏輯清晰

## 🛠️ 技術棧

**後端**: Django 5.0 + Scrapy + Playwright + PostgreSQL + Redis
**前端**: React 18 + TypeScript + Material-UI + Tailwind CSS (優化版)
**容器化**: Docker Multi-stage Build + ECR + Docker Compose
**測試**: Jest + Playwright + Django TestCase + PyTest
**CI/CD**: AWS EC2 Self-hosted Runner + ECR + GitHub Actions OIDC + 精確觸發邏輯
**基礎設施**: AWS Auto Scaling Group + Spot Instances + Golden AMI
**監控**: Prometheus + Percy + Lighthouse (智慧觸發)
**安全**: Doppler + 敏感信息掃描 + IAM 角色 + OIDC + 文檔構建觸發優化

## 🧪 測試和品質保證

### 多層次測試架構

#### 🔬 單元測試 (開發過程)

```bash
make test-backend          # Django 測試 (16 tests)
make test-frontend         # React/Jest 測試 (30 tests)
```

#### 🔗 整合測試 (提交前)

```bash
make test-integration      # 程式碼層面整合測試
# 包含: API 健康檢查 + 數據格式驗證 + 跨模組測試
```

#### 🎭 端到端測試 (發佈前)

```bash
make test-e2e             # Playwright E2E 測試
# 需要完整運行環境，模擬真實用戶操作
```

#### 🔄 CI 流程 (品質關卡)

```bash
make ci-check             # 本地 CI 完整流程
# 包含: 代碼格式檢查 + 單元測試 + 整合測試 + 安全掃描

# 手動安全檢查
cd frontend && pnpm audit                    # 檢查所有漏洞
cd frontend && pnpm audit --audit-level=high # 僅檢查高危漏洞
```

### 品質保證工具鏈

- **代碼品質**: Black + Flake8 + ESLint + Prettier
- **安全檢查**: 敏感信息掃描 + 大檔案監控
- **視覺測試**: Percy 視覺回歸測試
- **性能測試**: Lighthouse 自動評分
- **依賴安全**: GitHub Dependabot 掃描

## 🚀 部署

### Docker 部署

```bash
# 構建並啟動服務
docker-compose up -d

# 特定網站爬蟲部署
docker-compose -f infra/docker/docker-compose.ttkan.yml up -d

# 單獨啟動 Redis 供本地測試
docker-compose -f infra/docker/docker-compose.redis.yml up -d
```

### Prometheus 監控

```bash
# 啟動監控服務
docker-compose -f infra/docker/docker-compose.yml up -d prometheus

# 訪問監控面板
open http://localhost:9090
```

## 🕷️ 爬蟲開發指南

### 架構優化

採用 **Scrapy + Redis Queue + AsyncPG Worker** 解耦架構：

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Scrapy Spider │───▶│   Redis Queue   │───▶│  AsyncPG Worker │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Prometheus      │    │ Selector Config │    │   PostgreSQL    │
│ Monitoring      │    │ (YAML)          │    │   Database      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 網站適配器模式

```python
class CZBooksAdapter(BaseAdapter):
    """CZBooks.net 適配器 - 純靜態內容"""
    site_name = 'czbooks'
    requires_js = False

    def parse_novel(self, response):
        # 從 selectors/czbooks.yaml 加載選擇器
        selectors = self.load_selectors()
        return self.extract_data(response, selectors)
```

## 📚 文檔

- [📁 專案架構說明](docs/project-structure-annotated.md) - 完整檔案結構
- [🔨 Makefile 使用指南](docs/makefile-guide.md) - 開發工具指南
- [🚀 CI/CD 策略](docs/cicd-strategy.md) - CI/CD 架構與觸發邏輯
- [🧪 測試策略](docs/testing/) - 測試架構說明
- [🔐 安全指南](docs/security/) - 安全最佳實踐
- [📖 爬蟲文檔](docs/crawler/) - Sphinx 生成的爬蟲系統文檔

## 🤝 貢獻指南

### 開發工作流程

1. **環境準備**

   ```bash
   make install-deps      # 安裝所有依賴
   make quickstart        # 快速設置並啟動開發環境
   ```

2. **創建分支**

   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **開發過程**

   ```bash
   make dev              # 啟動開發環境
   make test             # 執行測試
   make lint             # 程式碼檢查
   ```

4. **提交前檢查**

   ```bash
   make ci-check         # 完整CI驗證
   git commit -m "[TAG] Subject: Description"
   ```

5. **創建 Pull Request**
   - 確保所有測試通過
   - 遵循 Squash & Merge 策略
   - 使用標記式 Commit Message 規範

### Commit Message 規範

```
[TAG] Subject: Body

範例:
[FEAT] Add multi-tier testing architecture: Implement unit/integration/e2e test strategy
[FIX] Resolve CI fixture loading issue: Add missing test_data.json for Django tests
[DOCS] Update project architecture: Reflect post-refactor structure
```

## 📊 項目統計

- **總檔案**: 595 個檔案 (優化後)
- **總目錄**: 150 個目錄 (重構後)
- **測試覆蓋**: Django 16 tests + React 30 tests
- **CI 時間**: 本地 3 秒，**Tier 2: 1-2 秒** ⚡ (99%+ 提升，歷史性突破)
- **代碼行數**: ~15,000 行 (不含依賴)
- **Node.js 優化**: 從 1.3GB 減至 769MB (**531MB 節省，41% 縮減**)
- **檔案數量優化**: 減少 87,258 個檔案
- **合併PR數**: 123個 (最新: 安全漏洞修復 + Docker 層快取優化 + CI 快取基礎設施建立)

## 📄 許可證

本項目基於 MIT 許可證 - 查看 [LICENSE](LICENSE) 文件了解詳情

## 🙏 致謝

- [Django](https://djangoproject.com/) - Web 框架
- [Scrapy](https://scrapy.org/) - 爬蟲框架
- [React](https://reactjs.org/) - 前端框架
- [Playwright](https://playwright.dev/) - 端到端測試
- [Percy](https://percy.io/) - 視覺回歸測試
- [GitHub Actions](https://github.com/features/actions) - CI/CD 平台

---

## 🏆 PR #118 完整專案優化成就 - 革命性突破

### ⚡ 歷史性性能里程碑 (2025-06-22)

**前所未有的 CI/CD 性能**:
- 前端測試: **125 秒 → 1 秒** (98.4% 提升)
- 後端測試: **38 秒 → 1 秒** (97.4% 提升)
- 總體提升: **99%+ 性能革命**

**📦 Node.js 依賴超級優化**:
- 總體積: **1.3GB → 769MB** (531MB 節省，**41% 縮減**)
- 檔案數量: 減少 **87,258 個檔案**
- 關鍵成果: 移除 @mui/icons-material (128MB)、@heroicons/react (21MB)
- 解決方案: 改用輕量內聯 SVG，零依賴負擔

**🏗️ Tier 2 技術架構特色**:

```bash
# 集中化映像管理
AWS ECR ← Docker Images ← GitHub Actions
    ↓
自託管 Runner (EC2) → 1秒啟動 → 測試完成
```

**🔐 企業級安全增強**:
- OIDC 認證 (GitHub ↔ AWS)
- 零硬編碼密鑰
- IAM 角色最小權限原則
- 自動映像安全掃描
- **可重用 Actions 安全管理**: 風險識別與文檔化
- **智慧條件觸發**: dorny/paths-filter 精確變更檢測

**💰 成本效益**:
- AWS Spot Instance: 70%+ 成本節省
- ECR 生命週期策略: 自動清理節省儲存
- 按需擴展: Auto Scaling Group

### 🎯 最新成就: PR #116 ESLint現代化 (2025-06-22)

**開發工具鏈現代化突破**:
- ✅ 解決 pre-commit 長期阻塞問題
- ✅ Docker 構建 100% 成功率
- ✅ 架構洞察: 多階段構建正確性驗證
- ✅ 代碼品質: 35個檔案自動統一風格

### 🎯 最新成就: PR #138 CI 資源管理優化 (2025-06-26)

**🚀 統一清理策略實施**:
- ✅ 創建 smart-cleanup action 支援三種清理級別 (basic/standard/aggressive)
- ✅ 移除 CI 配置中重複的清理步驟，統一使用智能清理
- ✅ 保護 BuildKit 快取卷，避免破壞跨映像快取共享
- ✅ 建立自動化測試驗證清理功能正確性

**🏗️ 鏡像構建重構**:
- ✅ 重構 frontend-ci.Dockerfile 為四階段模式 (base→global-tools→deps→ci-env)
- ✅ 實現 BuildKit cache sharing 機制，使用統一 cache ID
- ✅ 跨映像快取共享，frontend-tier2 和 frontend-ci 共享依賴快取
- ✅ 創建共享基礎 Dockerfile 結構，為未來標準化奠定基礎

**📊 實測性能提升**:
- ✅ **無變更重建**: 5s → 0s (100% 快取命中) ⚡
- ✅ **源碼變更重建**: 5s → 1s (80% 快取命中) 🚀
- ✅ **依賴變更重建**: 5s → 1s (80% 快取命中) 📈
- ✅ **整體快取效率**: 95% → 98% 🎯

**🔧 安全性與穩定性提升**:
- ✅ 修復危險的臨時檔案清理命令，改用基於時間的安全清理
- ✅ 移除危險的手動 Git pack 檔案清理，使用安全的 git gc
- ✅ 實施超時機制，防止清理過程卡住
- ✅ 完整的測試覆蓋，確保所有功能正確運作

### 🎯 歷史成就: PR #123 安全與性能雙重突破 (2025-06-24)

**🔐 安全漏洞完整修復**:
- ✅ 修復 7 個安全漏洞 (3個高危 + 1個中危 + 3個低危)
- ✅ 前端: tar-fs, ws, cookie, esbuild 漏洞修復
- ✅ 後端: requests, sentry-sdk, djangorestframework 漏洞修復
- ✅ 使用向後相容版本約束，確保系統穩定性

**⚡ CI/CD 性能基礎設施優化**:
- ✅ Docker 層快取優化: 共享基礎層架構，實現永久快取
- ✅ pnpm 版本一致性修復: 統一使用 pnpm@9.4.0
- ✅ 智能文檔構建觸發: 只在真正有文檔變更時才構建
- ✅ 快取基礎設施建立: 為未來 CI 執行奠定高效基礎

**📊 累積性能提升**:
- build-frontend-image: 目標從 6.5 分鐘 → < 2 分鐘
- test-frontend-optimized: 目標從 1.5 分鐘 → < 30 秒
- 首次運行已建立快取，後續執行將體驗質的飛躍

### 🚀 下一步: 黃金28內容基石

基礎設施已達企業級水準，安全性與性能雙重保障，專注於高價值內容開發。

---

**🚀 開始使用革命性的全端小說爬蟲系統！**

**快速啟動**: `make quickstart` → `make dev` → 體驗 99%+ 性能提升 + 531MB 瘦身效果！
