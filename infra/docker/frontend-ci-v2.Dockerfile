# Monorepo Frontend CI Dockerfile v2
# 全新設計：基於 Monorepo 架構的智能緩存前端映像
# 優化目標：最大化 pnpm 緩存效率，最小化構建時間

# ========================================
# Stage 1: Base Node.js Environment
# ========================================
FROM node:18-alpine AS base

# 安裝 pnpm 全局
RUN npm install -g pnpm@latest

# 設置工作目錄
WORKDIR /workspace

# 安裝系統依賴
RUN apk add --no-cache \
    git \
    curl \
    bash

# ========================================
# Stage 2: Dependencies Installation
# ========================================
FROM base AS dependencies

# 複製 pnpm 配置文件（這些文件變化頻率低，緩存效果好）
COPY pnpm-workspace.yaml ./
COPY package.json ./
COPY pnpm-lock.yaml ./

# 複製前端應用的 package.json（單獨複製以利用 Docker 層緩存）
COPY apps/web/package.json ./apps/web/

# 安裝依賴（這一層在 package.json 和 pnpm-lock.yaml 不變時會被緩存）
RUN pnpm install --frozen-lockfile

# ========================================
# Stage 3: Application Build
# ========================================
FROM dependencies AS builder

# 複製前端源代碼
COPY apps/web/public ./apps/web/public
COPY apps/web/src ./apps/web/src
# 分別複製配置文件以避免 Docker 緩存問題
COPY apps/web/tsconfig.json ./apps/web/
COPY apps/web/tailwind.config.js ./apps/web/
COPY apps/web/postcss.config.js ./apps/web/

# 設置工作目錄到前端應用
WORKDIR /workspace/apps/web

# 構建應用
RUN pnpm build

# ========================================
# Stage 4: Production Runtime
# ========================================
FROM node:18-alpine AS production

# 安裝 pnpm、serve 和運行時依賴
RUN npm install -g pnpm@latest serve && \
    apk add --no-cache curl bash

# 設置工作目錄
WORKDIR /workspace

# 複製 pnpm 配置
COPY pnpm-workspace.yaml ./
COPY package.json ./
COPY pnpm-lock.yaml ./

# 複製前端應用配置
COPY apps/web/package.json ./apps/web/

# 安裝生產依賴
RUN pnpm install --frozen-lockfile --prod

# 從構建階段複製構建產物
COPY --from=builder /workspace/apps/web/build ./apps/web/build
COPY --from=builder /workspace/apps/web/public ./apps/web/public

# 複製其他必要文件（React 應用配置）
COPY apps/web/package.json ./apps/web/

# 設置用戶權限
RUN addgroup -g 1001 -S ci-user && \
    adduser -S ci-user -u 1001 && \
    chown -R ci-user:ci-user /workspace

USER ci-user

# 設置工作目錄到前端應用
WORKDIR /workspace/apps/web

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/ || exit 1

# 標籤
LABEL maintainer="NovelWebsite DevOps Team"
LABEL version="2.0-monorepo-ci"
LABEL description="Monorepo Frontend CI image with intelligent caching"
LABEL tier="2.0-monorepo-ci"
LABEL optimization="Monorepo-aware with pnpm intelligent caching"

# 預設命令 - 使用 serve 來提供靜態文件
CMD ["npx", "serve", "-s", "build", "-l", "3000"]
