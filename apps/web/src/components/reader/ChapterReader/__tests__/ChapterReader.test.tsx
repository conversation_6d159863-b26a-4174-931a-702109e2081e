import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { act } from 'react';
import ChapterReader from '../ChapterReader';

// Mock useNavigate
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('ChapterReader', () => {
  const defaultProps = {
    title: '測試章節',
    content: '這是測試內容',
    prevChapter: '1',
    nextChapter: '3',
  };

  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('renders chapter title and content', () => {
    render(
      <MemoryRouter>
        <ChapterReader {...defaultProps} />
      </MemoryRouter>
    );

    expect(screen.getByText(defaultProps.title)).toBeInTheDocument();
    expect(screen.getByText(defaultProps.content)).toBeInTheDocument();
  });

  it('navigates to previous chapter when clicking previous button', async () => {
    render(
      <MemoryRouter>
        <ChapterReader {...defaultProps} />
      </MemoryRouter>
    );

    await act(async () => {
      fireEvent.click(screen.getByLabelText('previous chapter'));
    });
    expect(mockNavigate).toHaveBeenCalledWith('/chapter/1');
  });

  it('navigates to next chapter when clicking next button', async () => {
    render(
      <MemoryRouter>
        <ChapterReader {...defaultProps} />
      </MemoryRouter>
    );

    await act(async () => {
      fireEvent.click(screen.getByLabelText('next chapter'));
    });
    expect(mockNavigate).toHaveBeenCalledWith('/chapter/3');
  });

  it('disables navigation buttons when no prev/next chapter', () => {
    render(
      <MemoryRouter>
        <ChapterReader title={defaultProps.title} content={defaultProps.content} />
      </MemoryRouter>
    );

    const prevButton = screen.getByRole('button', { name: /previous chapter/i });
    const nextButton = screen.getByRole('button', { name: /next chapter/i });

    expect(prevButton).toBeDisabled();
    expect(nextButton).toBeDisabled();
  });
});
