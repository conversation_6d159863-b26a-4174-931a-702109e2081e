import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { act } from 'react';
import Register from '../Register';

// Simple mock for onRegister
const mockOnRegister = jest.fn(() => Promise.resolve());

describe('<Register />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders initial state with disabled submit', () => {
    render(<Register onRegister={mockOnRegister} />);

    expect(screen.getByLabelText('用戶名')).toHaveValue('');
    expect(screen.getByLabelText('電子郵件')).toHaveValue('');
    expect(screen.getByLabelText('密碼')).toHaveValue('');
    expect(screen.getByRole('button', { name: '註冊' })).toBeDisabled();
  });

  it('shows native validation messages for invalid email', async () => {
    const user = userEvent.setup();
    render(<Register onRegister={mockOnRegister} />);

    const emailInput = screen.getByLabelText('電子郵件') as HTMLInputElement;

    await act(async () => {
      await user.type(emailInput, 'invalid');
      await user.tab();
    });

    expect(emailInput).toBeInvalid();
  });

  it('shows validation messages when submitting invalid form', async () => {
    render(<Register onRegister={mockOnRegister} />);

    const submitButton = screen.getByRole('button', { name: '註冊' });
    const emailInput = screen.getByLabelText('電子郵件') as HTMLInputElement;

    // submit without filling in required fields
    await act(async () => {
      fireEvent.click(submitButton);
    });

    expect(emailInput.validationMessage).not.toBe('');
    expect(mockOnRegister).not.toHaveBeenCalled();
  });

  it('calls onRegister with form values on success', async () => {
    const user = userEvent.setup();
    render(<Register onRegister={mockOnRegister} />);

    await act(async () => {
      await user.type(screen.getByLabelText('用戶名'), 'tester');
      await user.type(screen.getByLabelText('電子郵件'), '<EMAIL>');
      await user.type(screen.getByLabelText('密碼'), '123456');
    });

    const button = screen.getByRole('button', { name: '註冊' });
    expect(button).toBeEnabled();

    await act(async () => {
      await user.click(button);
    });

    expect(mockOnRegister).toHaveBeenCalledWith('tester', '<EMAIL>', '123456');
  });

  it('sets password minimum length requirement', () => {
    render(<Register onRegister={mockOnRegister} />);

    const passwordInput = screen.getByLabelText('密碼') as HTMLInputElement;
    expect(passwordInput).toHaveAttribute('minLength', '6');
  });

  it('prevents submission with too short password', async () => {
    const user = userEvent.setup();
    render(<Register onRegister={mockOnRegister} />);

    await act(async () => {
      await user.type(screen.getByLabelText('用戶名'), 'tester');
      await user.type(screen.getByLabelText('電子郵件'), '<EMAIL>');
      const passwordInput = screen.getByLabelText('密碼') as HTMLInputElement;
      await user.type(passwordInput, '123');
      // jsdom doesn't enforce minLength, so simulate invalid state
      passwordInput.setCustomValidity('too short');
      await user.tab();
    });

    const passwordInput = screen.getByLabelText('密碼') as HTMLInputElement;
    expect(passwordInput).toBeInvalid();

    await act(async () => {
      await user.click(screen.getByRole('button', { name: '註冊' }));
    });

    expect(mockOnRegister).not.toHaveBeenCalled();
  });

  it('shows loading state during registration attempt and button text changes', async () => {
    const user = userEvent.setup();
    let resolveFn: () => void = () => {};
    const mockPending = jest.fn(
      () =>
        new Promise<void>(resolve => {
          resolveFn = resolve;
        })
    );
    render(<Register onRegister={mockPending} />);

    await act(async () => {
      await user.type(screen.getByLabelText('用戶名'), 'tester');
      await user.type(screen.getByLabelText('電子郵件'), '<EMAIL>');
      await user.type(screen.getByLabelText('密碼'), '123456');
    });

    const button = screen.getByRole('button', { name: '註冊' });
    expect(button).toBeEnabled();

    await act(async () => {
      await user.click(button);
    });

    expect(mockPending).toHaveBeenCalledTimes(1);
    expect(button).toBeDisabled();
    expect(button).toHaveTextContent('註冊中...');

    // Resolve the pending promise
    resolveFn();

    // Wait for the button text to change back
    await waitFor(() => {
      expect(screen.getByRole('button', { name: '註冊' })).toBeInTheDocument();
    });
  });

  it('re-enables submit button after registration error', async () => {
    const user = userEvent.setup();
    const mockReject = jest.fn(() => Promise.reject(new Error('fail')));
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    render(<Register onRegister={mockReject} />);

    await act(async () => {
      await user.type(screen.getByLabelText('用戶名'), 'tester');
      await user.type(screen.getByLabelText('電子郵件'), '<EMAIL>');
      await user.type(screen.getByLabelText('密碼'), '123456');
    });

    await act(async () => {
      await user.click(screen.getByRole('button', { name: '註冊' }));
    });

    await waitFor(() => {
      expect(screen.getByRole('button', { name: '註冊' })).toBeEnabled();
    });

    expect(consoleSpy).toHaveBeenCalled();
    consoleSpy.mockRestore();
  });
});
